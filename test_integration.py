#!/usr/bin/env python3
"""
Test script to verify the integration between extraction pipeline and regression evaluation.
"""

import sys
import os
from pathlib import Path

# Add regression_evaluation/src to the Python path
EVALUATION_SRC_DIR = os.path.abspath(os.path.join('regression_evaluation', 'src'))
if EVALUATION_SRC_DIR not in sys.path:
    sys.path.append(EVALUATION_SRC_DIR)

def test_imports():
    """Test if all required imports work."""
    print("Testing imports...")
    
    try:
        # Test extraction pipeline imports
        from src.extraction_pipeline_parallel_strurcture import (
            InMemoryDataLoader, 
            run_regression_evaluation,
            save_results
        )
        print("✓ Extraction pipeline imports successful")
    except ImportError as e:
        print(f"✗ Extraction pipeline import failed: {e}")
        return False
    
    try:
        # Test regression evaluation imports
        from main import ADCEvaluationApp
        from data_loader import FileDataLoader
        from models import ExtractedEndpoint
        print("✓ Regression evaluation imports successful")
    except ImportError as e:
        print(f"✗ Regression evaluation import failed: {e}")
        return False
    
    return True

def test_data_conversion():
    """Test data conversion between formats."""
    print("\nTesting data conversion...")
    
    # Sample extraction data (simplified)
    sample_data = [
        {
            "adc_name": "Test-ADC",
            "model_name": "Test-Model", 
            "endpoint_name": "antigen_expression",
            "endpoint_measurements": [
                {
                    "experiment_type": "IHC",
                    "measured_value": "High",
                    "citations": ["Test citation"]
                }
            ]
        }
    ]
    
    try:
        from models import ExtractedEndpoint
        
        # Test conversion
        paper_id = "test_paper"
        for row_data in sample_data:
            if "paper_id" not in row_data:
                row_data["paper_id"] = paper_id
            endpoint = ExtractedEndpoint(**row_data)
            print(f"✓ Successfully converted data: {endpoint.adc_name}")
        
        return True
    except Exception as e:
        print(f"✗ Data conversion failed: {e}")
        return False

def test_file_structure():
    """Test if required directories and files exist."""
    print("\nTesting file structure...")
    
    required_paths = [
        "regression_evaluation/data",
        "regression_evaluation/data/ground-truth",
        "regression_evaluation/data/extraction_results",
        "src/extraction_pipeline_parallel_strurcture.py"
    ]
    
    all_exist = True
    for path in required_paths:
        if Path(path).exists():
            print(f"✓ {path} exists")
        else:
            print(f"✗ {path} missing")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests."""
    print("=== Integration Test ===")
    
    tests = [
        ("Import Test", test_imports),
        ("Data Conversion Test", test_data_conversion), 
        ("File Structure Test", test_file_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n=== Test Summary ===")
    all_passed = True
    for test_name, passed in results:
        status = "PASS" if passed else "FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All tests passed! Integration should work.")
    else:
        print("\n❌ Some tests failed. Check the issues above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
